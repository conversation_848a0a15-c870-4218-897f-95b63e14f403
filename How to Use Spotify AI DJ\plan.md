# 文章创作执行计划

## 用户需求和目标
- **文章主题**: How to Use Spotify AI DJ
- **目标关键词**: How to Use Spotify AI DJ
- **文章长度**: 1600字（最多可超出20%，即最高1920字）
- **语言**: 英文
- **时间框架**: 基于2025年6月的最新数据和趋势
- **目标受众**: 音乐爱好者和创作者，专注于下载、编辑和分享音乐以获得更好的音频体验
- **开头策略**: D - Personal Experience/Case Study Opening
- **推荐产品**: Cinch Audio Recorder

## 内容质量要求
- **Effort (努力程度)**: 体现明显的人工成分和深度思考
- **Originality (原创性)**: 提供独特信息增量，包含3-5个其他文章未涵盖的独特观点
- **Talent/Skill (专业能力)**: 展示作者在该领域的专业知识和实际经验
- **Accuracy (准确性)**: 确保事实准确，避免错误信息

## 执行步骤详细清单

### 第1步：基础研究和大纲生成
- [x] 提取参考URL内容
- [x] 分析竞品文章结构和内容覆盖范围
- [x] 识别内容空白点和用户痛点
- [ ] 创建超级大纲 (super_outline.md)
- [ ] 优化为最终大纲 (final_outline.md)

### 第2步：内容创作
- [ ] 基于最终大纲撰写初稿 (first_draft.md)
- [ ] 确保字数控制在1600-1920字范围内
- [ ] 融入拟人化写作风格
- [ ] 添加个人经验和试错故事

### 第3步：SEO优化
- [ ] 生成SEO标题和元描述
- [ ] 创建featured image提示词
- [ ] 保存SEO内容至 seo_metadata_images.md

### 第4步：质量检查
- [ ] 验证字数是否符合要求
- [ ] 检查拟人化写作风格
- [ ] 验证链接有效性
- [ ] 确认图片添加

## 完成标准和检查点

### 大纲阶段检查点
- [ ] 是否包含至少3个竞品文章未涵盖的独特观点？
- [ ] 是否为每个H2章节准备了人工经验要素？
- [ ] 是否识别并准备解决用户的具体痛点？
- [ ] 字数分配是否合理且总和在目标范围内？

### 初稿阶段检查点
- [ ] 文章是否达到1600-1920字？
- [ ] 是否按照hl.md的拟人化写作要求？
- [ ] 是否避免了明显的AI语言和句子结构？
- [ ] 产品推荐是否自然融入？

### 最终检查点
- [ ] 内链和外链是否有效？
- [ ] 相关图片是否添加？
- [ ] SEO元素是否完整？
- [ ] 整体质量是否达到95+分标准？

## 预期输出文件清单
1. super_outline.md - 初始大纲
2. final_outline.md - 最终优化大纲
3. first_draft.md - 文章初稿
4. seo_metadata_images.md - SEO内容和图片提示词

## 独特价值点识别
基于研究发现的内容空白：
1. 用户对AI DJ语音交互功能的实际体验和限制
2. AI DJ在不同设备间的同步和兼容性问题
3. 与传统播放列表相比的实际优势和劣势
4. 针对音乐创作者的特殊使用场景
5. 离线音乐保存的需求和解决方案

## 人工经验要素准备
- 初次使用AI DJ的真实体验
- 语音命令功能的试错过程
- 不同音乐类型的AI推荐准确性测试
- 跨设备使用的实际问题和解决方案
- 与Cinch Audio Recorder结合使用的工作流程
